import { defineConfig } from 'vite'

export default defineConfig({
  server: {
    port: 3006,
    host: true,
    allowedHosts: [
      'docs.chhrone.web.id',
      'localhost',
      '127.0.0.1',
      '0.0.0.0'
    ],
    cors: {
      origin: true, // Allow all origins
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
    }
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets'
  }
})
