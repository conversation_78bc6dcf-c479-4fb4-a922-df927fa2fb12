const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');

const config = require('./config');
const { generalLimiter } = require('./middleware/rateLimiter');
const { errorHandler, notFoundHandler } = require('./middleware/errorHandler');
const { asyncRequestLogger } = require('./middleware/asyncLogger');
const { jsonParser, urlencodedParser } = require('./middleware/bodyParser');

// Import routes
const healthRoutes = require('./routes/health');
const apiRoutes = require('./routes/index');
const { socketIOProxy, documentationServiceProxy } = require('./middleware/proxy');

const app = express();

// ===== SECURITY MIDDLEWARE =====

// Helmet for security headers
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
  crossOriginEmbedderPolicy: false
}));

// CORS configuration
app.use(cors({
  origin: (origin, callback) => {
    // Allow requests with no origin (mobile apps, etc.)
    if (!origin) return callback(null, true);
    
    if (config.cors.allowedOrigins.includes(origin)) {
      return callback(null, true);
    }
    
    // In development, allow localhost with any port
    if (config.nodeEnv === 'development' && origin.includes('localhost')) {
      return callback(null, true);
    }
    
    return callback(new Error('Not allowed by CORS'));
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Service-Key', 'X-Internal-Service']
}));

// ===== GENERAL MIDDLEWARE =====

// Compression
app.use(compression());

// Body parsing - Skip for proxy routes to avoid conflicts
app.use(jsonParser);
app.use(urlencodedParser);

// Async Logging (replaces Morgan for better performance)
if (config.nodeEnv !== 'test') {
  app.use(asyncRequestLogger);
}

// Rate limiting
app.use(generalLimiter);

// Trust proxy (for accurate IP addresses)
app.set('trust proxy', 1);

// ===== ROUTES =====

// Health check routes
app.use('/health', healthRoutes);

// Documentation routes (direct access, not under /api)
app.use('/docs', (req, res, next) => {
  // Try to proxy to documentation service first
  documentationServiceProxy(req, res, (err) => {
    if (err) {
      // If documentation service is not available, serve a fallback page
      res.status(200).send(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>ATMA API Documentation</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .container { max-width: 800px; margin: 0 auto; }
            .error { color: #e74c3c; background: #fdf2f2; padding: 15px; border-radius: 5px; margin: 20px 0; }
            .info { color: #2c3e50; background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; }
          </style>
        </head>
        <body>
          <div class="container">
            <h1>ATMA API Documentation</h1>
            <div class="error">
              <strong>Documentation Service Unavailable</strong><br>
              The documentation service is currently not running on port 3006.
            </div>
            <div class="info">
              <h3>To fix this issue:</h3>
              <ol>
                <li>Navigate to the documentation-service directory</li>
                <li>Run: <code>npm install</code></li>
                <li>Run: <code>npm run dev</code></li>
                <li>The service should start on port 3006</li>
              </ol>
            </div>
            <h3>API Gateway Information</h3>
            <p><strong>Base URL:</strong> https://api.chhrone.web.id/</p>
            <p><strong>Documentation URL:</strong> https://docs.chhrone.web.id/</p>
            <p><strong>Health Check:</strong> <a href="/health">/health</a></p>
          </div>
        </body>
        </html>
      `);
    }
  });
});
app.use('/documentation', documentationServiceProxy);

// API routes
app.use('/api', apiRoutes);

// Socket.IO proxy route (untuk WebSocket connections)
app.use('/socket.io', socketIOProxy);

// Root endpoint - Handle documentation domain and redirect to documentation
app.get('/', (req, res) => {
  // Check if request comes from docs domain
  const host = req.get('host') || '';
  const acceptHeader = req.get('Accept') || '';

  if (host.includes('docs.chhrone.web.id')) {
    // Documentation domain - redirect to docs
    res.redirect('/docs');
  } else if (acceptHeader.includes('text/html')) {
    // Browser request from API domain - redirect to documentation
    res.redirect('/docs');
  } else {
    // API request - return JSON info
    res.json({
      success: true,
      message: 'ATMA API Gateway is running',
      version: '1.0.0',
      timestamp: new Date().toISOString(),
      services: {
        auth: `${config.services.auth}`,
        archive: `${config.services.archive}`,
        assessment: `${config.services.assessment}`,
        notification: `${config.services.notification}`,
        documentation: `${config.services.documentation}`
      },
      documentation: {
        health: '/health',
        detailedHealth: '/health/detailed',
        ready: '/health/ready',
        live: '/health/live',
        apiDocs: '/docs',
        interactiveDocs: '/documentation'
      }
    });
  }
});

// API Info endpoint (always returns JSON)
app.get('/api', (req, res) => {
  res.json({
    success: true,
    message: 'ATMA API Gateway is running',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    services: {
      auth: `${config.services.auth}`,
      archive: `${config.services.archive}`,
      assessment: `${config.services.assessment}`,
      notification: `${config.services.notification}`,
      documentation: `${config.services.documentation}`
    },
    documentation: {
      health: '/health',
      detailedHealth: '/health/detailed',
      ready: '/health/ready',
      live: '/health/live',
      apiDocs: '/docs',
      interactiveDocs: '/documentation'
    }
  });
});

// ===== ERROR HANDLING =====

// 404 handler
app.use(notFoundHandler);

// Global error handler
app.use(errorHandler);

module.exports = app;
