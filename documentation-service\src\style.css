@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Custom fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap');

/* Base styles */
* {
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  line-height: 1.6;
  color: #374151;
  background-color: #f9fafb;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Navigation active state */
.nav-link.active {
  background-color: #f3f4f6;
  color: #111827;
  font-weight: 500;
}

/* Sidebar styling */
aside {
  background-color: #fafafa;
  border-right: 1px solid #e5e7eb;
}

/* Navigation links */
.nav-link {
  font-size: 0.875rem;
  line-height: 1.25rem;
  border-radius: 0.375rem;
  transition: all 0.15s ease-in-out;
}

.nav-link:hover {
  background-color: #f9fafb;
  color: #374151;
}

/* Code block enhancements */
.code-block-container {
  position: relative;
  margin: 1.5rem 0;
}

.copy-btn {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: rgba(55, 65, 81, 0.8);
  color: white;
  border: none;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 10;
}

.copy-btn:hover {
  background: rgba(55, 65, 81, 1);
  transform: translateY(-1px);
}

/* Search modal enhancements */
#searchModal {
  backdrop-filter: blur(4px);
}

#searchInput {
  font-size: 1rem;
  transition: all 0.2s ease;
}

#searchInput:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Responsive design */
@media (max-width: 1024px) {
  aside {
    width: 16rem;
  }
}

@media (max-width: 768px) {
  aside {
    position: fixed;
    left: -100%;
    transition: left 0.3s ease;
    z-index: 40;
    height: 100vh;
    top: 0;
    background-color: white;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  aside.open {
    left: 0;
  }

  main {
    margin-left: 0;
  }
}

/* Syntax highlighting overrides */
.hljs {
  background: #1f2937 !important;
  color: #f9fafb !important;
}

/* Table styling */
table {
  border-collapse: collapse;
  width: 100%;
  margin: 1.5rem 0;
  background: white;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

th, td {
  padding: 0.75rem 1rem;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

th {
  background-color: #f8fafc;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

tr:last-child td {
  border-bottom: none;
}

tr:hover {
  background-color: #f8fafc;
}

/* Alert/Note styling */
.alert {
  padding: 1rem;
  border-radius: 0.5rem;
  margin: 1.5rem 0;
  border-left: 4px solid;
}

.alert-warning {
  background-color: #fef3c7;
  border-color: #f59e0b;
  color: #92400e;
}

.alert-info {
  background-color: #dbeafe;
  border-color: #3b82f6;
  color: #1e40af;
}

.alert-danger {
  background-color: #fee2e2;
  border-color: #ef4444;
  color: #dc2626;
}

/* Badge styling */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.badge-primary {
  background-color: #dbeafe;
  color: #1e40af;
}

.badge-success {
  background-color: #d1fae5;
  color: #065f46;
}

.badge-warning {
  background-color: #fef3c7;
  color: #92400e;
}

.badge-danger {
  background-color: #fee2e2;
  color: #dc2626;
}

/* Loading animation */
.loading {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 50%;
  border-top-color: #3b82f6;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Smooth transitions */
* {
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
}

/* Focus styles */
button:focus,
input:focus,
a:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  aside {
    display: none;
  }
  
  main {
    margin-left: 0;
    max-width: none;
  }
  
  .copy-btn {
    display: none;
  }
  
  header {
    position: static;
  }
}
