# ATMA API Documentation Service

A modern, interactive documentation service for the ATMA (AI-Driven Talent Mapping Assessment) API Gateway, built with HTML, JavaScript, Tailwind CSS, and Vite.

## Features

- 📖 **Interactive Documentation**: Clean, modern interface for browsing API documentation
- 🔍 **Search Functionality**: Real-time search across all API endpoints and content
- 🎨 **Responsive Design**: Works perfectly on desktop, tablet, and mobile devices
- 🎯 **Smart Navigation**: Auto-generated navigation with smooth scrolling
- 📋 **Copy Code Blocks**: One-click copying of code examples
- 🌙 **Theme Support**: Ready for dark mode implementation
- ⚡ **Fast Performance**: Built with Vite for optimal loading speeds
- 🎨 **Beautiful UI**: Styled with Tailwind CSS for a professional look

## Technology Stack

- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **Markdown Processing**: Marked.js
- **Syntax Highlighting**: Highlight.js
- **Fonts**: Inter (UI) + JetBrains Mono (Code)

## Quick Start

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn

### Installation

1. Navigate to the documentation service directory:
```bash
cd documentation-service
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and visit `http://localhost:5173`

### Build for Production

```bash
npm run build
```

The built files will be in the `dist` directory.

### Preview Production Build

```bash
npm run preview
```

## Project Structure

```
documentation-service/
├── index.html              # Main HTML file
├── package.json            # Dependencies and scripts
├── vite.config.js          # Vite configuration
├── tailwind.config.js      # Tailwind CSS configuration
├── postcss.config.js       # PostCSS configuration
├── src/
│   ├── main.js            # Main JavaScript application
│   └── style.css          # Custom styles and Tailwind imports
└── README.md              # This file
```

## Features Overview

### Navigation
- Auto-generated sidebar navigation from markdown headers
- Smooth scrolling to sections
- Active section highlighting
- Collapsible subsections

### Search
- Real-time search across all content
- Keyboard shortcut support (Ctrl/Cmd + K)
- Search results with context
- Quick navigation to found content

### Code Blocks
- Syntax highlighting for multiple languages
- Copy-to-clipboard functionality
- Responsive code blocks
- Language detection

### Responsive Design
- Mobile-first approach
- Collapsible sidebar on mobile
- Touch-friendly interface
- Optimized for all screen sizes

## Customization

### Adding New Content
The API documentation content is embedded in `src/main.js` in the `apiDocumentation` variable. To update the documentation:

1. Edit the markdown content in the `apiDocumentation` string
2. The navigation and content will automatically update

### Styling
- Modify `tailwind.config.js` for theme customization
- Add custom styles in `src/style.css`
- Update color scheme in the Tailwind configuration

### Configuration
- Vite settings: `vite.config.js`
- Tailwind settings: `tailwind.config.js`
- PostCSS settings: `postcss.config.js`

## Browser Support

- Chrome/Chromium (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## Performance

- Optimized bundle size with Vite
- Lazy loading of syntax highlighting
- Efficient search implementation
- Minimal runtime dependencies

## Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build

### Development Features

- Hot module replacement (HMR)
- Fast refresh
- Source maps
- Development server with proxy support

## Deployment

The documentation service can be deployed to any static hosting service:

1. **Netlify**: Connect your repository and set build command to `npm run build`
2. **Vercel**: Import project and deploy with zero configuration
3. **GitHub Pages**: Use GitHub Actions to build and deploy
4. **Traditional Web Server**: Upload the `dist` folder contents

### Environment Variables

No environment variables are required for basic functionality.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is part of the ATMA backend system.

## Support

For technical support or questions:
- Check the main ATMA backend documentation
- Review the API Gateway documentation
- Contact the development team

---

**Last Updated**: 2024-01-21
**Version**: 1.0.0
